import 'package:flutter/foundation.dart';
import '../../data/datasources/auth_datasource.dart';
import '../../core/services/auth_service.dart';
import '../../core/services/logging_service.dart';

import '../../core/errors/exceptions.dart';

enum AuthState {
  initial,
  loading,
  guest,
  authenticated,
  error,
}

class AuthProvider extends ChangeNotifier {
  final AuthDataSource authDataSource;
  final AuthService authService;

  AuthProvider({
    required this.authDataSource,
    required this.authService,
  });

  AuthState _state = AuthState.initial;
  String? _errorMessage;
  Map<String, dynamic>? _currentUser;

  // Getters
  AuthState get state => _state;
  String? get errorMessage => _errorMessage;
  Map<String, dynamic>? get currentUser => _currentUser;
  bool get isAuthenticated => _state == AuthState.authenticated;
  bool get isGuest => _state == AuthState.guest;
  bool get isLoading => _state == AuthState.loading;
  bool get canUseApp => isAuthenticated || isGuest;

  // Initialize auth state - defaults to guest mode
  Future<void> initialize() async {
    _setState(AuthState.loading);

    try {
      if (authService.isAuthenticated && !authService.isTokenExpired()) {
        // Try to get current user info
        final user = await authDataSource.getCurrentUser();
        _currentUser = user;
        _setState(AuthState.authenticated);
      } else {
        // Default to guest mode - no authentication required
        _setState(AuthState.guest);
      }
    } catch (e) {
      // If token is invalid, clear auth and set to guest mode
      await authService.clearAuth();
      _setState(AuthState.guest);
    }
  }

  // Login
  Future<bool> login(String username, String password) async {
    _setState(AuthState.loading);
    _clearError();

    try {
      final response = await authDataSource.login(username, password);
      _currentUser = response['user'] as Map<String, dynamic>?;
      _setState(AuthState.authenticated);
      return true;
    } catch (e) {
      _setError(_getErrorMessage(e));
      _setState(AuthState.guest);
      return false;
    }
  }

  // Register
  Future<bool> register({
    required String email,
    required String username,
    required String password,
    required String fullName,
  }) async {
    _setState(AuthState.loading);
    _clearError();

    try {
      final response = await authDataSource.register(
        email: email,
        username: username,
        password: password,
        fullName: fullName,
      );
      
      // If registration includes auto-login
      if (response['access_token'] != null) {
        _currentUser = response['user'] as Map<String, dynamic>?;
        _setState(AuthState.authenticated);
      } else {
        _setState(AuthState.guest);
      }

      return true;
    } catch (e) {
      _setError(_getErrorMessage(e));
      _setState(AuthState.guest);
      return false;
    }
  }

  // Continue as guest (skip authentication)
  void continueAsGuest() {
    _setState(AuthState.guest);
    _clearError();
  }

  // Login with Gmail using Google Sign-In (not implemented)
  Future<bool> loginWithGmail() async {
    _setState(AuthState.loading);
    _clearError();

    try {
      // Gmail login is not implemented - redirect to guest mode
      LoggingService.info('Gmail login not implemented, using guest mode');
      _setError('Gmail login is not available. Using guest mode.');
      _setState(AuthState.guest);
      return false;
    } catch (e) {
      LoggingService.error('Gmail login failed: $e');
      _setError('Gmail login failed: ${e.toString()}');
      _setState(AuthState.guest);
      return false;
    }
  }

  // Logout - returns to guest mode
  Future<void> logout() async {
    _setState(AuthState.loading);

    try {
      if (isAuthenticated) {
        // Check if user is signed in with Google
        if (_currentUser?['provider'] == 'gmail') {
          final googleSignIn = GoogleSignInService.instance;
          await googleSignIn.signOutWithDataPreservation();
        } else {
          // Regular backend logout
          await authDataSource.logout();
        }
      }
    } catch (e) {
      // Continue with logout even if server call fails
      debugPrint('Logout error: $e');
    } finally {
      _currentUser = null;
      _setState(AuthState.guest); // Return to guest mode instead of requiring login
    }
  }

  // Refresh token
  Future<bool> refreshToken() async {
    try {
      await authDataSource.refreshToken();
      return true;
    } catch (e) {
      // If refresh fails, logout user
      await logout();
      return false;
    }
  }

  // Update user data
  Future<void> updateUserData() async {
    if (!isAuthenticated) return;

    try {
      final user = await authDataSource.getCurrentUser();
      _currentUser = user;
      notifyListeners();
    } catch (e) {
      debugPrint('Failed to update user data: $e');
    }
  }

  // Helper methods
  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  String _getErrorMessage(dynamic error) {
    if (error is ServerException) {
      return error.message;
    } else if (error is NetworkException) {
      return error.message;
    } else {
      return 'An unexpected error occurred';
    }
  }

  // User info getters
  String? get userId => _currentUser?['id']?.toString();
  String? get userEmail => _currentUser?['email']?.toString();
  String? get userName => _currentUser?['full_name']?.toString() ?? _currentUser?['username']?.toString();
  String? get userUsername => _currentUser?['username']?.toString();
  bool get isAdmin => _currentUser?['is_superuser'] == true;
  int get userPoints => _currentUser?['points'] as int? ?? 0;
  int get userLevel => _currentUser?['level'] as int? ?? 1;
  int get userStreak => _currentUser?['current_streak'] as int? ?? 0;
}
