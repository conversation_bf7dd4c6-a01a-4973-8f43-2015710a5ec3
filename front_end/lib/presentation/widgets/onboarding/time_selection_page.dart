import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../../providers/preferences_provider.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/time_utils.dart';

/// Enhanced time selection page with fluid animations
class TimeSelectionPage extends StatefulWidget {
  final bool isActive;
  final VoidCallback? onSelectionChanged;

  const TimeSelectionPage({
    super.key,
    this.isActive = false,
    this.onSelectionChanged,
  });

  @override
  State<TimeSelectionPage> createState() => _TimeSelectionPageState();
}

class _TimeSelectionPageState extends State<TimeSelectionPage>
    with TickerProviderStateMixin {
  late AnimationController _headerController;
  late AnimationController _contentController;
  late AnimationController _timeSlotController;
  
  late Animation<double> _headerFadeAnimation;
  late Animation<Offset> _headerSlideAnimation;
  late Animation<double> _contentFadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _headerController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _contentController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _timeSlotController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _setupAnimations();
    
    if (widget.isActive) {
      _startAnimations();
    }
  }

  void _setupAnimations() {
    _headerFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _headerController,
      curve: Curves.easeOut,
    ));
    
    _headerSlideAnimation = Tween<Offset>(
      begin: const Offset(0.0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _headerController,
      curve: Curves.easeOutCubic,
    ));
    
    _contentFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _contentController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOut),
    ));
  }

  void _startAnimations() async {
    await Future.delayed(const Duration(milliseconds: 100));
    _headerController.forward();
    
    await Future.delayed(const Duration(milliseconds: 200));
    _contentController.forward();
  }

  @override
  void didUpdateWidget(TimeSelectionPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.isActive != widget.isActive && widget.isActive) {
      _startAnimations();
    }
  }

  @override
  void dispose() {
    _headerController.dispose();
    _contentController.dispose();
    _timeSlotController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Animated header
          _buildAnimatedHeader(theme),
          
          const SizedBox(height: 32),
          
          // Content
          Expanded(
            child: _buildAnimatedContent(theme),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedHeader(ThemeData theme) {
    return AnimatedBuilder(
      animation: _headerController,
      builder: (context, child) {
        return Transform.translate(
          offset: _headerSlideAnimation.value * 50,
          child: Opacity(
            opacity: _headerFadeAnimation.value,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 4,
                      height: 32,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: theme.customColors.primaryGradient,
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'Notification Times',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                )
                .animate(delay: 200.ms)
                .slideX(begin: -0.3, end: 0, duration: 600.ms),
                
                const SizedBox(height: 16),
                
                Text(
                  'Choose when you\'d like to receive your daily motivation. Perfect timing makes all the difference!',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    height: 1.5,
                  ),
                )
                .animate(delay: 400.ms)
                .fadeIn(duration: 600.ms)
                .slideY(begin: 0.3, end: 0),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnimatedContent(ThemeData theme) {
    return AnimatedBuilder(
      animation: _contentController,
      builder: (context, child) {
        return Opacity(
          opacity: _contentFadeAnimation.value,
          child: Consumer<PreferencesProvider>(
            builder: (context, preferencesProvider, child) {
              return SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Random times option
                    _buildRandomTimesCard(theme, preferencesProvider),
                    
                    const SizedBox(height: 24),
                    
                    // Divider with text
                    _buildDivider(theme),
                    
                    const SizedBox(height: 24),
                    
                    // Time slots
                    _buildTimeSlots(theme, preferencesProvider),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildRandomTimesCard(ThemeData theme, PreferencesProvider preferencesProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primaryContainer.withOpacity(0.3),
            theme.colorScheme.primaryContainer.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.shuffle,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Smart Timing',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    Text(
                      'Let us choose optimal times between 8 AM - 8 PM',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: FilledButton.tonal(
              onPressed: () => _generateRandomTimes(preferencesProvider),
              style: FilledButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.auto_awesome,
                    size: 18,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Generate Smart Times',
                    style: TextStyle(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    )
    .animate(delay: 600.ms)
    .scaleXY(begin: 0.95, end: 1.0, duration: 600.ms, curve: Curves.easeOutCubic)
    .then()
    .shimmer(duration: 2000.ms, color: theme.colorScheme.primary.withOpacity(0.1));
  }

  Widget _buildDivider(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  theme.colorScheme.outline.withOpacity(0.5),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'Or set specific times',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Container(
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  theme.colorScheme.outline.withOpacity(0.5),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),
      ],
    )
    .animate(delay: 800.ms)
    .fadeIn(duration: 600.ms);
  }

  Widget _buildTimeSlots(ThemeData theme, PreferencesProvider preferencesProvider) {
    return Column(
      children: List.generate(
        preferencesProvider.notificationCount,
        (index) {
          final time = index < preferencesProvider.notificationTimes.length
              ? preferencesProvider.notificationTimes[index]
              : const TimeOfDay(hour: 9, minute: 0);

          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _AnimatedTimeSlot(
              index: index,
              time: time,
              onTimeChanged: (newTime) {
                preferencesProvider.setNotificationTime(index, newTime);
                widget.onSelectionChanged?.call();
              },
            ),
          );
        },
      ),
    );
  }

  void _generateRandomTimes(PreferencesProvider preferencesProvider) {
    final count = preferencesProvider.notificationCount;
    final times = <TimeOfDay>[];
    
    // Generate evenly spaced times between 8 AM and 8 PM
    const startHour = 8;
    const endHour = 20;
    final totalHours = endHour - startHour;
    final interval = totalHours / count;
    
    for (int i = 0; i < count; i++) {
      final hour = (startHour + (i * interval)).round();
      final minute = (i % 2 == 0) ? 0 : 30; // Alternate between :00 and :30
      times.add(TimeOfDay(hour: hour, minute: minute));
    }
    
    preferencesProvider.setNotificationTimes(times);
    widget.onSelectionChanged?.call();
    
    // Trigger animation
    _timeSlotController.forward().then((_) {
      _timeSlotController.reverse();
    });
  }
}

class _AnimatedTimeSlot extends StatefulWidget {
  final int index;
  final TimeOfDay time;
  final ValueChanged<TimeOfDay> onTimeChanged;

  const _AnimatedTimeSlot({
    required this.index,
    required this.time,
    required this.onTimeChanged,
  });

  @override
  State<_AnimatedTimeSlot> createState() => _AnimatedTimeSlotState();
}

class _AnimatedTimeSlotState extends State<_AnimatedTimeSlot>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _getTimeDescription(TimeOfDay time) {
    final hour = time.hour;
    if (hour >= 5 && hour < 12) return 'Morning motivation';
    if (hour >= 12 && hour < 17) return 'Afternoon inspiration';
    if (hour >= 17 && hour < 21) return 'Evening reflection';
    return 'Night thoughts';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: theme.colorScheme.outline.withOpacity(0.3),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Number indicator
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: theme.customColors.primaryGradient,
                    ),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      '${widget.index + 1}',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notification ${widget.index + 1}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _getTimeDescription(widget.time),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Time display and button
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      TimeUtils.formatTimeOfDay(widget.time),
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () => _selectTime(context),
                      icon: const Icon(Icons.access_time),
                      tooltip: 'Change time',
                      style: IconButton.styleFrom(
                        backgroundColor: theme.colorScheme.primaryContainer,
                        foregroundColor: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    )
    .animate(delay: (widget.index * 150).ms)
    .scaleXY(begin: 0.0, end: 1.0, duration: 600.ms, curve: Curves.elasticOut)
    .then()
    .shimmer(
      duration: 2000.ms,
      color: theme.colorScheme.primary.withOpacity(0.1),
    );
  }

  Future<void> _selectTime(BuildContext context) async {
    try {
      final TimeOfDay? newTime = await showTimePicker(
        context: context,
        initialTime: widget.time,
        helpText: 'Select notification time',
        cancelText: 'Cancel',
        confirmText: 'Set Time',
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              timePickerTheme: TimePickerThemeData(
                backgroundColor: Theme.of(context).colorScheme.surface,
                hourMinuteTextColor: Theme.of(context).colorScheme.primary,
                dayPeriodTextColor: Theme.of(context).colorScheme.primary,
                dialHandColor: Theme.of(context).colorScheme.primary,
                dialBackgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                entryModeIconColor: Theme.of(context).colorScheme.primary,
              ),
            ),
            child: child!,
          );
        },
      );

      if (newTime != null) {
        widget.onTimeChanged(newTime);
        
        // Trigger animation
        _controller.forward().then((_) {
          _controller.reverse();
        });
      }
    } catch (e) {
      // Handle error silently
    }
  }
}
