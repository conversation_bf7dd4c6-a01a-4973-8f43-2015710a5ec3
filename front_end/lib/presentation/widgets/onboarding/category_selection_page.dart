import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../../../core/constants/app_constants.dart';
import '../../providers/preferences_provider.dart';

/// Enhanced category selection page with fluid animations
class CategorySelectionPage extends StatefulWidget {
  final bool isActive;
  final VoidCallback? onSelectionChanged;

  const CategorySelectionPage({
    super.key,
    this.isActive = false,
    this.onSelectionChanged,
  });

  @override
  State<CategorySelectionPage> createState() => _CategorySelectionPageState();
}

class _CategorySelectionPageState extends State<CategorySelectionPage>
    with TickerProviderStateMixin {
  late AnimationController _headerController;
  late AnimationController _gridController;
  late AnimationController _selectionController;
  
  late Animation<double> _headerFadeAnimation;
  late Animation<Offset> _headerSlideAnimation;
  late Animation<double> _gridFadeAnimation;
  late Animation<double> _selectionScaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _headerController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _gridController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _selectionController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _setupAnimations();
    
    if (widget.isActive) {
      _startAnimations();
    }
  }

  void _setupAnimations() {
    _headerFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _headerController,
      curve: Curves.easeOut,
    ));
    
    _headerSlideAnimation = Tween<Offset>(
      begin: const Offset(0.0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _headerController,
      curve: Curves.easeOutCubic,
    ));
    
    _gridFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _gridController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOut),
    ));
    
    _selectionScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _selectionController,
      curve: Curves.easeInOut,
    ));
  }

  void _startAnimations() async {
    await Future.delayed(const Duration(milliseconds: 100));
    _headerController.forward();
    
    await Future.delayed(const Duration(milliseconds: 200));
    _gridController.forward();
  }

  @override
  void didUpdateWidget(CategorySelectionPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.isActive != widget.isActive && widget.isActive) {
      _startAnimations();
    }
  }

  @override
  void dispose() {
    _headerController.dispose();
    _gridController.dispose();
    _selectionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Animated header
          _buildAnimatedHeader(theme),
          
          const SizedBox(height: 32),
          
          // Selection counter
          _buildSelectionCounter(theme),
          
          const SizedBox(height: 24),
          
          // Animated category grid
          Expanded(
            child: _buildAnimatedCategoryGrid(theme),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedHeader(ThemeData theme) {
    return AnimatedBuilder(
      animation: _headerController,
      builder: (context, child) {
        return Transform.translate(
          offset: _headerSlideAnimation.value * 50,
          child: Opacity(
            opacity: _headerFadeAnimation.value,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 4,
                      height: 32,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: theme.customColors.primaryGradient,
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'Choose Your Interests',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                )
                .animate(delay: 200.ms)
                .slideX(begin: -0.3, end: 0, duration: 600.ms),
                
                const SizedBox(height: 16),
                
                Text(
                  'Select the categories that inspire you most. We\'ll personalize your daily motivation based on your choices.',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    height: 1.5,
                  ),
                )
                .animate(delay: 400.ms)
                .fadeIn(duration: 600.ms)
                .slideY(begin: 0.3, end: 0),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSelectionCounter(ThemeData theme) {
    return Consumer<PreferencesProvider>(
      builder: (context, preferencesProvider, child) {
        final selectedCount = preferencesProvider.selectedCategories.length;
        
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: selectedCount > 0 
                ? theme.colorScheme.primaryContainer
                : theme.colorScheme.surfaceVariant,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: selectedCount > 0 
                  ? theme.colorScheme.primary
                  : theme.colorScheme.outline,
              width: selectedCount > 0 ? 2 : 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                selectedCount > 0 ? Icons.check_circle : Icons.radio_button_unchecked,
                color: selectedCount > 0 
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurfaceVariant,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                selectedCount > 0 
                    ? '$selectedCount categories selected'
                    : 'No categories selected',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: selectedCount > 0 
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurfaceVariant,
                  fontWeight: selectedCount > 0 ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        )
        .animate(target: selectedCount > 0 ? 1 : 0)
        .scaleXY(begin: 0.95, end: 1.0, duration: 200.ms)
        .then()
        .shimmer(
          duration: 1000.ms,
          color: theme.colorScheme.primary.withOpacity(0.2),
        );
      },
    );
  }

  Widget _buildAnimatedCategoryGrid(ThemeData theme) {
    return AnimatedBuilder(
      animation: _gridController,
      builder: (context, child) {
        return Opacity(
          opacity: _gridFadeAnimation.value,
          child: Consumer<PreferencesProvider>(
            builder: (context, preferencesProvider, child) {
              return LayoutBuilder(
                builder: (context, constraints) {
                  const itemMinWidth = 160.0;
                  final crossAxisCount = (constraints.maxWidth / itemMinWidth)
                      .floor()
                      .clamp(2, 4);
                  
                  return GridView.builder(
                    physics: const BouncingScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: crossAxisCount,
                      childAspectRatio: 1.2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                    ),
                    itemCount: AppConstants.availableCategories.length,
                    itemBuilder: (context, index) {
                      final category = AppConstants.availableCategories[index];
                      final isSelected = preferencesProvider.selectedCategories.contains(category);
                      
                      return _AnimatedCategoryCard(
                        category: category,
                        isSelected: isSelected,
                        index: index,
                        onTap: () {
                          preferencesProvider.toggleCategory(category);
                          widget.onSelectionChanged?.call();
                          
                          // Trigger selection animation
                          _selectionController.forward().then((_) {
                            _selectionController.reverse();
                          });
                        },
                      );
                    },
                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}

class _AnimatedCategoryCard extends StatefulWidget {
  final String category;
  final bool isSelected;
  final int index;
  final VoidCallback onTap;

  const _AnimatedCategoryCard({
    required this.category,
    required this.isSelected,
    required this.index,
    required this.onTap,
  });

  @override
  State<_AnimatedCategoryCard> createState() => _AnimatedCategoryCardState();
}

class _AnimatedCategoryCardState extends State<_AnimatedCategoryCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _hoverAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _hoverAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  String _getCategoryDisplayName(String category) {
    return category.split('_').map((word) => 
      word[0].toUpperCase() + word.substring(1)
    ).join(' ');
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'motivation': return Icons.rocket_launch;
      case 'success': return Icons.emoji_events;
      case 'happiness': return Icons.sentiment_very_satisfied;
      case 'wisdom': return Icons.psychology;
      case 'courage': return Icons.shield;
      case 'love': return Icons.favorite;
      case 'health': return Icons.health_and_safety;
      case 'wealth': return Icons.attach_money;
      case 'leadership': return Icons.groups;
      case 'creativity': return Icons.palette;
      case 'mindfulness': return Icons.self_improvement;
      case 'perseverance': return Icons.trending_up;
      default: return Icons.star;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final displayName = _getCategoryDisplayName(widget.category);
    final icon = _getCategoryIcon(widget.category);

    return AnimatedBuilder(
      animation: _hoverAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _hoverAnimation.value,
          child: GestureDetector(
            onTap: widget.onTap,
            onTapDown: (_) {
              _hoverController.forward();
            },
            onTapUp: (_) {
              _hoverController.reverse();
            },
            onTapCancel: () {
              _hoverController.reverse();
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOutCubic,
              decoration: BoxDecoration(
                gradient: widget.isSelected
                    ? LinearGradient(
                        colors: theme.customColors.primaryGradient,
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      )
                    : null,
                color: widget.isSelected ? null : theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: widget.isSelected 
                      ? Colors.transparent
                      : theme.colorScheme.outline,
                  width: widget.isSelected ? 0 : 1,
                ),
                boxShadow: widget.isSelected ? [
                  BoxShadow(
                    color: theme.customColors.primaryGradient.first.withOpacity(0.3),
                    blurRadius: 12,
                    spreadRadius: 2,
                    offset: const Offset(0, 4),
                  ),
                ] : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      icon,
                      size: 32,
                      color: widget.isSelected 
                          ? Colors.white
                          : theme.colorScheme.primary,
                    )
                    .animate(target: widget.isSelected ? 1 : 0)
                    .scaleXY(begin: 1.0, end: 1.2, duration: 200.ms)
                    .then()
                    .scaleXY(begin: 1.2, end: 1.0, duration: 200.ms),
                    
                    const SizedBox(height: 12),
                    
                    Text(
                      displayName,
                      textAlign: TextAlign.center,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: widget.isSelected ? FontWeight.bold : FontWeight.w500,
                        color: widget.isSelected 
                            ? Colors.white
                            : theme.colorScheme.onSurface,
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      width: widget.isSelected ? 24 : 0,
                      height: 2,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(1),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    )
    .animate(delay: (widget.index * 100).ms)
    .scaleXY(begin: 0.0, end: 1.0, duration: 600.ms, curve: Curves.elasticOut)
    .then()
    .shimmer(
      duration: 2000.ms,
      color: theme.colorScheme.primary.withOpacity(0.1),
    );
  }
}
