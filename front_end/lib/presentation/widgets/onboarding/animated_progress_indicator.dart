import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/theme/app_theme.dart';

/// Enhanced animated progress indicator for onboarding
class AnimatedProgressIndicator extends StatefulWidget {
  final double progress;
  final int currentStep;
  final int totalSteps;
  final bool showStepNumbers;
  final Color? primaryColor;
  final Color? backgroundColor;
  final double height;
  final Duration animationDuration;

  const AnimatedProgressIndicator({
    super.key,
    required this.progress,
    required this.currentStep,
    required this.totalSteps,
    this.showStepNumbers = true,
    this.primaryColor,
    this.backgroundColor,
    this.height = 6.0,
    this.animationDuration = const Duration(milliseconds: 600),
  });

  @override
  State<AnimatedProgressIndicator> createState() => _AnimatedProgressIndicatorState();
}

class _AnimatedProgressIndicatorState extends State<AnimatedProgressIndicator>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _progressController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOutCubic,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _progressController.forward();
    _pulseController.repeat(reverse: true);
  }

  @override
  void didUpdateWidget(AnimatedProgressIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.progress != widget.progress) {
      _progressAnimation = Tween<double>(
        begin: _progressAnimation.value,
        end: widget.progress,
      ).animate(CurvedAnimation(
        parent: _progressController,
        curve: Curves.easeInOutCubic,
      ));
      
      _progressController.reset();
      _progressController.forward();
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final customColors = theme.customColors;
    final primaryColor = widget.primaryColor ?? customColors.primaryGradient.first;
    final backgroundColor = widget.backgroundColor ?? theme.colorScheme.surfaceVariant;

    return Column(
      children: [
        // Step indicators
        if (widget.showStepNumbers) ...[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Row(
              children: List.generate(widget.totalSteps, (index) {
                final isActive = index <= widget.currentStep;
                final isCurrent = index == widget.currentStep;
                
                return Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: AnimatedBuilder(
                          animation: _pulseAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: isCurrent ? _pulseAnimation.value : 1.0,
                              child: Container(
                                height: 32,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: isActive ? primaryColor : backgroundColor,
                                  border: Border.all(
                                    color: isActive ? primaryColor : theme.colorScheme.outline,
                                    width: 2,
                                  ),
                                  boxShadow: isCurrent ? [
                                    BoxShadow(
                                      color: primaryColor.withOpacity(0.3),
                                      blurRadius: 8,
                                      spreadRadius: 2,
                                    ),
                                  ] : null,
                                ),
                                child: Center(
                                  child: AnimatedSwitcher(
                                    duration: const Duration(milliseconds: 300),
                                    child: isActive && index < widget.currentStep
                                        ? Icon(
                                            Icons.check,
                                            key: ValueKey('check_$index'),
                                            color: Colors.white,
                                            size: 16,
                                          )
                                        : Text(
                                            '${index + 1}',
                                            key: ValueKey('number_$index'),
                                            style: theme.textTheme.labelMedium?.copyWith(
                                              color: isActive ? Colors.white : theme.colorScheme.onSurfaceVariant,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      if (index < widget.totalSteps - 1)
                        Expanded(
                          flex: 2,
                          child: Container(
                            height: 2,
                            margin: const EdgeInsets.symmetric(horizontal: 8),
                            decoration: BoxDecoration(
                              color: index < widget.currentStep ? primaryColor : backgroundColor,
                              borderRadius: BorderRadius.circular(1),
                            ),
                          )
                          .animate(delay: (index * 100).ms)
                          .scaleX(
                            begin: 0.0,
                            end: 1.0,
                            duration: 400.ms,
                            curve: Curves.easeOutCubic,
                          ),
                        ),
                    ],
                  ),
                );
              }),
            ),
          )
          .animate()
          .fadeIn(duration: 600.ms)
          .slideY(begin: -0.5, end: 0),
          
          const SizedBox(height: 24),
        ],
        
        // Progress bar
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Progress text
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Step ${widget.currentStep + 1} of ${widget.totalSteps}',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Text(
                    '${(widget.progress * 100).round()}%',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: primaryColor,
                    ),
                  ),
                ],
              )
              .animate()
              .fadeIn(duration: 400.ms)
              .slideX(begin: -0.3, end: 0),
              
              const SizedBox(height: 8),
              
              // Animated progress bar
              Container(
                height: widget.height,
                decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: BorderRadius.circular(widget.height / 2),
                ),
                child: AnimatedBuilder(
                  animation: _progressAnimation,
                  builder: (context, child) {
                    return Stack(
                      children: [
                        // Background
                        Container(
                          height: widget.height,
                          decoration: BoxDecoration(
                            color: backgroundColor,
                            borderRadius: BorderRadius.circular(widget.height / 2),
                          ),
                        ),
                        
                        // Progress fill
                        FractionallySizedBox(
                          widthFactor: _progressAnimation.value,
                          child: Container(
                            height: widget.height,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: customColors.primaryGradient,
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                              borderRadius: BorderRadius.circular(widget.height / 2),
                              boxShadow: [
                                BoxShadow(
                                  color: primaryColor.withOpacity(0.3),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                          ),
                        ),
                        
                        // Shimmer effect
                        if (_progressAnimation.value > 0)
                          FractionallySizedBox(
                            widthFactor: _progressAnimation.value,
                            child: Container(
                              height: widget.height,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(widget.height / 2),
                              ),
                            )
                            .animate(onPlay: (controller) => controller.repeat())
                            .shimmer(
                              duration: 1500.ms,
                              color: Colors.white.withOpacity(0.3),
                            ),
                          ),
                      ],
                    );
                  },
                ),
              )
              .animate()
              .scaleX(begin: 0.0, end: 1.0, duration: 800.ms, curve: Curves.easeOutCubic)
              .then()
              .shimmer(duration: 2000.ms, color: Colors.white.withOpacity(0.2)),
            ],
          ),
        ),
      ],
    );
  }
}
